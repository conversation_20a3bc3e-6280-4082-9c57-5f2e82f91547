<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';">
    <title>Electron App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            color: white;
            font-size: 2.5em;
            font-weight: 300;
            margin-bottom: 10px;
        }

        .header p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1em;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 40px;
            text-align: center;
        }

        .welcome-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
            margin-bottom: 30px;
        }

        .welcome-card h2 {
            color: #333;
            font-size: 2em;
            margin-bottom: 20px;
            font-weight: 400;
        }

        .welcome-card p {
            color: #666;
            font-size: 1.1em;
            line-height: 1.6;
            margin-bottom: 30px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .info-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .info-item h3 {
            color: #333;
            font-size: 1.1em;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .info-item p {
            color: #666;
            font-size: 0.9em;
            margin: 0;
        }

        .buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
            display: none;
        }

        .status.show {
            display: block;
        }

        .footer {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 15px;
            text-align: center;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .footer p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9em;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }
            
            .welcome-card {
                padding: 30px 20px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Electron App</h1>
        <p>Modern desktop application built with Electron</p>
    </div>

    <div class="main-content">
        <div class="welcome-card">
            <h2>Welcome to Your Electron App!</h2>
            <p>This is a modern Electron application built with security best practices and a clean architecture. The app demonstrates proper IPC communication, context isolation, and modern UI design.</p>
            
            <div class="info-grid">
                <div class="info-item">
                    <h3>App Version</h3>
                    <p id="app-version">Loading...</p>
                </div>
                <div class="info-item">
                    <h3>Platform</h3>
                    <p id="platform">Loading...</p>
                </div>
                <div class="info-item">
                    <h3>Electron Version</h3>
                    <p id="electron-version">Loading...</p>
                </div>
                <div class="info-item">
                    <h3>Node.js Version</h3>
                    <p id="node-version">Loading...</p>
                </div>
            </div>

            <div class="buttons">
                <button class="btn" id="async-btn">Test Async Operation</button>
                <button class="btn secondary" id="info-btn">Show App Info</button>
            </div>

            <div class="status" id="status"></div>
        </div>
    </div>

    <div class="footer">
        <p>Built with ❤️ using Electron • Ready for development</p>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
