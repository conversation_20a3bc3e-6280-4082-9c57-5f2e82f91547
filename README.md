# Electron App

A modern Electron application built with security best practices and clean architecture.

## Features

- ✅ Modern Electron setup with security best practices
- ✅ Context isolation and preload scripts
- ✅ IPC communication between main and renderer processes
- ✅ Responsive UI with modern design
- ✅ Cross-platform menu system
- ✅ Development tools integration
- ✅ Error handling and logging

## Project Structure

```
electron-app/
├── main.js          # Main process (Node.js)
├── preload.js       # Preload script for secure IPC
├── index.html       # Renderer process UI
├── renderer.js      # Renderer process logic
├── package.json     # Project configuration
├── assets/          # Static assets (icons, images)
└── README.md        # This file
```

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn

### Installation

1. Clone or download this project
2. Install dependencies:
   ```bash
   npm install
   ```

### Running the Application

```bash
# Start the application
npm start

# Start in development mode (with DevTools)
npm run dev
```

### Available Scripts

- `npm start` - Start the Electron application
- `npm run dev` - Start in development mode with DevTools
- `npm run clean` - Clean node_modules and reinstall dependencies

## Security Features

This application implements modern Electron security best practices:

- **Context Isolation**: Enabled to prevent renderer from accessing Node.js APIs directly
- **Node Integration**: Disabled in renderer process
- **Preload Scripts**: Used for secure communication between main and renderer
- **Content Security Policy**: Implemented to prevent XSS attacks
- **External Link Handling**: External links open in default browser, not in app

## Architecture

### Main Process (`main.js`)
- Creates and manages application windows
- Handles system events and application lifecycle
- Manages application menu
- Handles IPC communication with renderer processes

### Preload Script (`preload.js`)
- Provides secure bridge between main and renderer processes
- Exposes limited APIs to renderer through `contextBridge`
- Maintains security while enabling necessary functionality

### Renderer Process (`index.html` + `renderer.js`)
- Handles UI and user interactions
- Communicates with main process through exposed APIs
- Implements responsive design and modern UI patterns

## Development

### Adding New Features

1. **IPC Communication**: Add new IPC handlers in `main.js` and expose them in `preload.js`
2. **UI Components**: Modify `index.html` and `renderer.js` for new UI features
3. **Menu Items**: Update the menu template in `main.js`

### Building for Production

This template is ready for development. For production builds, consider adding:
- Electron Builder or Electron Forge for packaging
- Code signing for distribution
- Auto-updater functionality
- Crash reporting

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Resources

- [Electron Documentation](https://www.electronjs.org/docs)
- [Electron Security Guidelines](https://www.electronjs.org/docs/tutorial/security)
- [Electron Best Practices](https://www.electronjs.org/docs/tutorial/best-practices)
