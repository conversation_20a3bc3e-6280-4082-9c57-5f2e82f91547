{"name": "electron-app", "version": "1.0.0", "description": "A modern Electron application built with best practices", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "echo \"Build script not configured yet\"", "lint": "echo \"<PERSON><PERSON> not configured yet\"", "test": "echo \"Error: no test specified\" && exit 1", "clean": "rm -rf node_modules package-lock.json && npm install"}, "keywords": ["electron", "desktop", "app"], "author": "Planfuly Inc.", "type": "commonjs", "devDependencies": {"electron": "^38.0.0"}}