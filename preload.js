const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App information
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  getPlatform: () => ipcRenderer.invoke('get-platform'),
  
  // Example async operation
  performAsyncOperation: () => ipcRenderer.invoke('perform-async-operation'),
  
  // Example of sending data to main process
  sendMessage: (message) => ipcRenderer.invoke('send-message', message),
  
  // Example of listening to events from main process
  onUpdateAvailable: (callback) => ipcRenderer.on('update-available', callback),
  
  // Remove listeners
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
});

// Example of exposing Node.js APIs safely
contextBridge.exposeInMainWorld('nodeAPI', {
  // Path utilities
  joinPath: (...paths) => require('path').join(...paths),
  
  // OS information
  platform: process.platform,
  arch: process.arch
});

// Log that preload script has loaded
console.log('Preload script loaded successfully');
