// Renderer process script
// This script runs in the renderer process and has access to the DOM
// It communicates with the main process through the preload script

document.addEventListener('DOMContentLoaded', async () => {
    console.log('Renderer process loaded');

    // Check if electronAPI is available (from preload script)
    if (typeof window.electronAPI === 'undefined') {
        console.error('electronAPI not available. Make sure preload script is loaded.');
        showStatus('Error: electronAPI not available', 'error');
        return;
    }

    // Load app information
    await loadAppInfo();

    // Set up event listeners
    setupEventListeners();

    // Show welcome message
    showStatus('Application loaded successfully!', 'success');
});

async function loadAppInfo() {
    try {
        // Get app version from main process
        const appVersion = await window.electronAPI.getAppVersion();
        document.getElementById('app-version').textContent = appVersion;

        // Get platform information
        const platform = await window.electronAPI.getPlatform();
        document.getElementById('platform').textContent = getPlatformName(platform);

        // Get Electron and Node.js versions
        document.getElementById('electron-version').textContent = process.versions.electron || 'N/A';
        document.getElementById('node-version').textContent = process.versions.node || 'N/A';

    } catch (error) {
        console.error('Error loading app info:', error);
        showStatus('Error loading app information', 'error');
    }
}

function setupEventListeners() {
    // Async operation button
    const asyncBtn = document.getElementById('async-btn');
    if (asyncBtn) {
        asyncBtn.addEventListener('click', handleAsyncOperation);
    }

    // Info button
    const infoBtn = document.getElementById('info-btn');
    if (infoBtn) {
        infoBtn.addEventListener('click', showAppInfo);
    }

    // Handle keyboard shortcuts
    document.addEventListener('keydown', (event) => {
        // Ctrl/Cmd + R for refresh (handled by Electron)
        if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
            console.log('Refresh requested');
        }

        // Ctrl/Cmd + Shift + I for DevTools (handled by Electron)
        if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'I') {
            console.log('DevTools requested');
        }
    });
}

async function handleAsyncOperation() {
    const button = document.getElementById('async-btn');
    const originalText = button.textContent;
    
    try {
        // Disable button and show loading state
        button.disabled = true;
        button.textContent = 'Processing...';
        
        showStatus('Performing async operation...', 'info');

        // Call the async operation in main process
        const result = await window.electronAPI.performAsyncOperation();
        
        showStatus(result, 'success');
        console.log('Async operation result:', result);

    } catch (error) {
        console.error('Async operation failed:', error);
        showStatus('Async operation failed: ' + error.message, 'error');
    } finally {
        // Re-enable button
        button.disabled = false;
        button.textContent = originalText;
    }
}

function showAppInfo() {
    const info = {
        'App Name': 'Electron App',
        'Version': document.getElementById('app-version').textContent,
        'Platform': document.getElementById('platform').textContent,
        'Electron': document.getElementById('electron-version').textContent,
        'Node.js': document.getElementById('node-version').textContent,
        'Chrome': process.versions.chrome || 'N/A',
        'V8': process.versions.v8 || 'N/A'
    };

    let infoText = 'Application Information:\n\n';
    for (const [key, value] of Object.entries(info)) {
        infoText += `${key}: ${value}\n`;
    }

    // Create a simple modal-like display
    const modal = createInfoModal(infoText);
    document.body.appendChild(modal);
}

function createInfoModal(content) {
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    `;

    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
        background: white;
        padding: 30px;
        border-radius: 15px;
        max-width: 500px;
        width: 90%;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    `;

    const pre = document.createElement('pre');
    pre.style.cssText = `
        font-family: 'Courier New', monospace;
        font-size: 14px;
        line-height: 1.5;
        margin: 0 0 20px 0;
        color: #333;
        white-space: pre-wrap;
    `;
    pre.textContent = content;

    const closeBtn = document.createElement('button');
    closeBtn.textContent = 'Close';
    closeBtn.className = 'btn';
    closeBtn.style.cssText = `
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 20px;
        cursor: pointer;
        font-size: 14px;
    `;

    closeBtn.addEventListener('click', () => {
        document.body.removeChild(modal);
    });

    // Close on background click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });

    modalContent.appendChild(pre);
    modalContent.appendChild(closeBtn);
    modal.appendChild(modalContent);

    return modal;
}

function showStatus(message, type = 'info') {
    const statusEl = document.getElementById('status');
    if (!statusEl) return;

    statusEl.textContent = message;
    statusEl.className = 'status show';

    // Set color based on type
    switch (type) {
        case 'success':
            statusEl.style.background = '#e8f5e8';
            statusEl.style.borderColor = '#4caf50';
            statusEl.style.color = '#2e7d32';
            break;
        case 'error':
            statusEl.style.background = '#ffebee';
            statusEl.style.borderColor = '#f44336';
            statusEl.style.color = '#c62828';
            break;
        case 'info':
        default:
            statusEl.style.background = '#e3f2fd';
            statusEl.style.borderColor = '#2196f3';
            statusEl.style.color = '#1565c0';
            break;
    }

    // Auto-hide after 5 seconds for success/info messages
    if (type !== 'error') {
        setTimeout(() => {
            statusEl.classList.remove('show');
        }, 5000);
    }
}

function getPlatformName(platform) {
    const platformNames = {
        'win32': 'Windows',
        'darwin': 'macOS',
        'linux': 'Linux',
        'freebsd': 'FreeBSD',
        'openbsd': 'OpenBSD',
        'sunos': 'SunOS'
    };
    return platformNames[platform] || platform;
}

// Handle any uncaught errors in the renderer process
window.addEventListener('error', (event) => {
    console.error('Renderer error:', event.error);
    showStatus('An error occurred: ' + event.error.message, 'error');
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    showStatus('Promise rejection: ' + event.reason, 'error');
});

// Export functions for potential testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        loadAppInfo,
        showStatus,
        getPlatformName
    };
}
